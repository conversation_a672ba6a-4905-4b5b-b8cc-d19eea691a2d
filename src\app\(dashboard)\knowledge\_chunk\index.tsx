'use client';
import type { PaginationProps } from 'antd';
import { Divider, Pagination, Space, Spin, message } from 'antd';
import dynamic from 'next/dynamic';
import { useCallback, useState } from 'react';
import EmptyData from '../_components/EmptyData';
import { useFetchNextChunkList, useSwitchChunk } from '../_hooks/chunk-hooks';
import ChunkCard from './chunk-card';
import CreatingModal from './chunk-creating-modal';
import ChunkToolBar from './chunk-toolbar';
import {
  useChangeChunkTextMode,
  useDeleteChunkByIds,
  useGetChunkHighlights,
  useHandleChunkCardClick,
  useUpdateChunk,
} from './hooks';

const DocumentPreview = dynamic(() => import('./document-preview/preview'), {
  ssr: false,
  loading: () => <Spin />,
});
const Chunk = () => {
  const {
    data: { documentInfo, data = [], total },
    pagination,
    loading,
    searchString,
    handleInputChange,
    available,
    handleSetAvailable,
  } = useFetchNextChunkList();
  const {
    chunkUpdatingLoading,
    onChunkUpdatingOk,
    showChunkUpdatingModal,
    hideChunkUpdatingModal,
    chunkId,
    chunkUpdatingVisible,
    documentId,
  } = useUpdateChunk();

  const { handleChunkCardClick, selectedChunkId } = useHandleChunkCardClick();
  const { changeChunkTextMode, textMode } = useChangeChunkTextMode();
  const [selectedChunkIds, setSelectedChunkIds] = useState<string[]>([]);
  const [isShowBatchOperate, setIsShowBatchOperate] = useState(false);
  const isPdf = documentInfo?.type === 'pdf';

  const { switchChunk } = useSwitchChunk();

  const { highlights, setWidthAndHeight } =
    useGetChunkHighlights(selectedChunkId);

  const handleSingleCheckboxClick = useCallback(
    (chunkId: string, checked: boolean) => {
      setSelectedChunkIds(previousIds => {
        const idx = previousIds.findIndex(x => x === chunkId);
        const nextIds = [...previousIds];
        if (checked && idx === -1) {
          nextIds.push(chunkId);
        } else if (!checked && idx !== -1) {
          nextIds.splice(idx, 1);
        }
        return nextIds;
      });
    },
    []
  );

  const { removeChunk } = useDeleteChunkByIds();

  const selectAllChunk = useCallback(
    (checked: boolean) => {
      setSelectedChunkIds(checked ? data.map(x => x.chunk_id) : []);
    },
    [data]
  );

  const onPaginationChange: PaginationProps['onShowSizeChange'] = (
    page,
    size
  ) => {
    setSelectedChunkIds([]);
    pagination.onChange?.(page, size);
  };

  const showSelectedChunkWarning = useCallback(() => {
    message.warning('请选择解析块');
  }, []);

  const handleRemoveChunk = useCallback(async () => {
    if (selectedChunkIds.length > 0) {
      const resCode: number = await removeChunk(selectedChunkIds, documentId);
      if (resCode === 0) {
        setSelectedChunkIds([]);
        setIsShowBatchOperate(false);
      }
    } else {
      showSelectedChunkWarning();
    }
  }, [selectedChunkIds, documentId, removeChunk, showSelectedChunkWarning]);

  const handleCancelBatchOperate = useCallback(() => {
    setSelectedChunkIds([]);
    setIsShowBatchOperate(false);
  }, [selectedChunkIds, isShowBatchOperate, documentId]);

  const handleOpenBatchOperate = useCallback(() => {
    setIsShowBatchOperate(true);
  }, [isShowBatchOperate]);

  const handleSwitchChunk = useCallback(
    async (available?: number, chunkIds?: string[]) => {
      let ids = chunkIds;
      if (!chunkIds) {
        ids = selectedChunkIds;
        if (selectedChunkIds.length === 0) {
          showSelectedChunkWarning();
          return;
        }
      }

      const resCode: number = await switchChunk({
        chunk_ids: ids,
        available_int: available,
        doc_id: documentId,
      });
      if (!chunkIds && resCode === 0) {
      }
    },
    [switchChunk, documentId, selectedChunkIds, showSelectedChunkWarning]
  );
  return (
    <>
      <ChunkToolBar
        selectAllChunk={selectAllChunk}
        createChunk={showChunkUpdatingModal}
        removeChunk={handleRemoveChunk}
        checked={selectedChunkIds.length === data.length}
        switchChunk={handleSwitchChunk}
        changeChunkTextMode={changeChunkTextMode}
        searchString={searchString}
        handleInputChange={handleInputChange}
        available={available}
        total={total}
        handleSetAvailable={handleSetAvailable}
        setSelectedChunkIds={selectedChunkIds}
        cancelBatchOperate={handleCancelBatchOperate}
        openBatchOperate={handleOpenBatchOperate}
        showBatchOperate={isShowBatchOperate}
      ></ChunkToolBar>
      <Divider></Divider>
      <div className="basis-0 grow flex gap-4 overflow-hidden w-[calc(100%+18px)] -ml-[9px] px-[9px]">
        {/* 左侧：ChunkCard 列表 */}
        <div
          className={`${
            isPdf ? 'w-[calc(60%+18px)]' : 'w-[calc(100%+24px)]'
          }  -ml-[12px] h-full overflow-y-auto relative`}
        >
          {data.length ? (
            <Space
              className="w-full p-[12px]"
              direction="vertical"
              size={'middle'}
            >
              {data.map((item, index) => (
                <ChunkCard
                  item={item}
                  key={item.chunk_id}
                  removeChunk={selectedChunkIds =>
                    removeChunk(selectedChunkIds, documentId)
                  }
                  editChunk={showChunkUpdatingModal}
                  checked={selectedChunkIds.some(x => x === item.chunk_id)}
                  handleCheckboxClick={handleSingleCheckboxClick}
                  switchChunk={handleSwitchChunk}
                  clickChunkCard={handleChunkCardClick}
                  selected={item.chunk_id === selectedChunkId}
                  textMode={textMode}
                  rowIndex={
                    (pagination.current! - 1) * pagination.pageSize! + index
                  }
                  showBatchOperate={isShowBatchOperate}
                ></ChunkCard>
              ))}
            </Space>
          ) : (
            <EmptyData />
          )}
        </div>

        {/* 右侧：DocumentPreview */}
        {isPdf && (
          <div className="w-[40%] border-l border-gray-200 pl-4">
            <DocumentPreview
              highlights={highlights}
              setWidthAndHeight={setWidthAndHeight}
            ></DocumentPreview>
          </div>
        )}
      </div>
      <Pagination
        className="!py-[10px]"
        {...pagination}
        total={total}
        size={'small'}
        onChange={onPaginationChange}
      />

      {chunkUpdatingVisible && (
        <CreatingModal
          doc_id={documentId}
          chunkId={chunkId}
          hideModal={hideChunkUpdatingModal}
          visible={chunkUpdatingVisible}
          loading={chunkUpdatingLoading}
          onOk={onChunkUpdatingOk}
          parserId={documentInfo.parser_id}
        />
      )}
    </>
  );
};

export default Chunk;

import type { IDocumentInfo } from '@/interfaces/document';
import { formatDate } from '@/utils/date';
import { getExtension } from '@/utils/document-util';
import { formatBytes } from '@/utils/formatBytes';
import { Flex, PaginationProps, Typography } from 'antd';
import { TableRowSelection } from 'antd/es/table/interface';
import React from 'react';
import ParsingActionCell from '../../dataset/parsing-action-cell';
import ParsingStatusCell from '../../dataset/parsing-status-cell';
import FolderSvg from '../../svg/folder.svg';
import PdfSvg from '../../svg/pdf.svg';
import TxtSvg from '../../svg/txt.svg';
import EmptyData from '../EmptyData';
import GenericList, { IColumnConfig } from '../GenericList';

const { Text } = Typography;

/**
 * 文档列表组件属性接口
 */
export interface IDocumentListProps {
  /** 文档数据源 */
  documents: IDocumentInfo[];
  /** 行选择配置 */
  rowSelection?: TableRowSelection<IDocumentInfo>;
  /** 是否显示选择模式 */
  showSelection?: boolean;
  /** 分页配置 */
  pagination: PaginationProps;
  /** 文档点击事件 */
  onDocumentClick?: (documentId: string) => void;
  /** 设置当前记录 */
  setCurrentRecord?: (record: IDocumentInfo) => void;
  /** 显示重命名弹窗 */
  showRenameModal?: () => void;
  /** 显示更改解析器弹窗 */
  showChangeParserModal?: () => void;
  /** 显示设置元数据弹窗 */
  showSetMetaModal?: () => void;
}

/**
 * 获取文件扩展名对应的SVG图标
 * @param name 文件名
 * @returns SVG图标组件
 */
const getExtensionSvg = (name: string): React.ReactNode => {
  const ext = getExtension(name);
  if (ext === 'txt') {
    return <TxtSvg className="size-md" />;
  } else if (ext === 'pdf') {
    return <PdfSvg className="size-md" />;
  } else {
    return <FolderSvg />;
  }
  return null;
};

/**
 * 文档列表组件
 * @param props 组件属性
 * @returns JSX元素
 */
const DocumentList: React.FC<IDocumentListProps> = props => {
  const {
    documents,
    rowSelection,
    showSelection = false,
    pagination,
    onDocumentClick,
    setCurrentRecord,
    showRenameModal,
    showChangeParserModal,
    showSetMetaModal,
  } = props;

  /**
   * 处理文档点击事件
   * @param record 文档记录
   */
  const handleDocumentClick = (record: IDocumentInfo) => {
    onDocumentClick?.(record.id);
  };

  /**
   * 定义列配置
   */
  const columns: IColumnConfig<IDocumentInfo>[] = [
    {
      key: 'name',
      title: '文件名称',
      width: 'flex-1 min-w-0',
      render: (record: IDocumentInfo) => (
        <div
          className="cursor-pointer"
          onClick={() => handleDocumentClick(record)}
        >
          <Flex gap={10} align="center">
            {getExtensionSvg(record.name)}
            <Text
              ellipsis={{ tooltip: record.name }}
              className="!text-[#000000B8]"
            >
              {record.name}
            </Text>
          </Flex>
        </div>
      ),
    },
    {
      key: 'type',
      title: '文件类型',
      width: 'w-20',
      render: (record: IDocumentInfo) => {
        const ext = getExtension(record.name);
        return (
          <span className="bg-[#f5f5f5] rounded-[4px] text-[#939393] text-[12px] p-[2px_6px] inline-block">
            {ext}
          </span>
        );
      },
    },
    {
      key: 'size',
      title: '文件大小',
      width: 'w-24',
      render: (record: IDocumentInfo) => {
        const byte = formatBytes(record.size);
        return <span className="text-[#999] text-[12px]">{byte}</span>;
      },
    },
    {
      key: 'chunk_num',
      title: '分块数',
      width: 'w-24',
      render: (record: IDocumentInfo) => (
        <span className="bg-[#f5f5f5] rounded-[4px] text-[#444] text-[12px] p-[2px_6px] inline-block">
          {record.chunk_num} 分段
        </span>
      ),
    },
    {
      key: 'status',
      title: '解析状态',
      width: 'w-32',
      render: (record: IDocumentInfo) => <ParsingStatusCell record={record} />,
    },
    {
      key: 'create_time',
      title: '上传日期',
      width: 'w-32',
      render: (record: IDocumentInfo) => (
        <span className="text-[#00000066] text-[12px]">
          {formatDate(record.create_time, 'YYYY-MM-DD HH:mm')}
        </span>
      ),
    },
  ];

  // 如果不是选择模式，添加操作列
  if (
    !showSelection &&
    setCurrentRecord &&
    showRenameModal &&
    showChangeParserModal &&
    showSetMetaModal
  ) {
    columns.push({
      key: 'actions',
      title: '操作',
      width: 'w-32',
      align: 'right',
      render: (record: IDocumentInfo) => (
        <ParsingActionCell
          setCurrentRecord={setCurrentRecord}
          showRenameModal={showRenameModal}
          showChangeParserModal={showChangeParserModal}
          showSetMetaModal={showSetMetaModal}
          record={record}
        />
      ),
    });
  }

  return (
    <GenericList<IDocumentInfo>
      className="relative grow-1 basis-0"
      dataSource={documents}
      columns={columns}
      rowSelection={rowSelection}
      showSelection={showSelection}
      pagination={pagination}
      rowKey="id"
      emptyText={<EmptyData />}
    />
  );
};

export default DocumentList;

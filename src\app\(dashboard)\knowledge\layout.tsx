'use client';
import { StyleProvider } from '@ant-design/cssinjs';
import '@ant-design/v5-patch-for-react-19';
import { App, ConfigProvider, ThemeConfig } from 'antd';
import zhCN from 'antd/locale/zh_CN';

const themeConfig: ThemeConfig = {
  components: {},
  token: {
    controlHeightLG: 37, // 较高的组件高度
    fontSizeLG: 14, // 大号字体大小
  },
};

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <StyleProvider layer>
      <ConfigProvider locale={zhCN} theme={themeConfig} componentSize="large">
        <App className="h-full">
          <div className="h-full flex flex-col px-6 py-4">{children}</div>
        </App>
      </ConfigProvider>
    </StyleProvider>
  );
}
